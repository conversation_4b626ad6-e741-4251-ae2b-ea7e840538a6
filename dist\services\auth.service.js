"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const db_1 = require("../config/db");
const user_model_1 = require("../models/user.model");
class AuthService {
    constructor() {
        this.userRepository = db_1.AppDataSource.getRepository(user_model_1.User);
    }
    // Generate JWT token
    generateToken(userId) {
        const secret = process.env.JWT_SECRET;
        if (!secret) {
            throw new Error('JWT_SECRET is not defined in environment variables');
        }
        return jsonwebtoken_1.default.sign({ userId }, secret, { expiresIn: process.env.JWT_EXPIRES_IN || '7d' });
    }
    // Verify JWT token
    verifyToken(token) {
        const secret = process.env.JWT_SECRET;
        if (!secret) {
            throw new Error('JWT_SECRET is not defined in environment variables');
        }
        return jsonwebtoken_1.default.verify(token, secret);
    }
    // User signup
    async signup(signupData) {
        try {
            const { username, email, password, phone, role } = signupData;
            // Check if user already exists
            const existingUser = await this.userRepository.findOne({
                where: [{ email }, { username }]
            });
            if (existingUser) {
                return {
                    success: false,
                    message: existingUser.email === email
                        ? 'User with this email already exists'
                        : 'Username already taken'
                };
            }
            // Create new user
            const user = new user_model_1.User();
            user.id = user_model_1.User.generateUserId();
            user.username = username;
            user.email = email;
            user.password = password; // Will be hashed by @BeforeInsert
            user.phone = phone || '';
            user.role = role || 'Student';
            // Save user (password will be automatically hashed)
            const savedUser = await this.userRepository.save(user);
            // Generate token
            const token = this.generateToken(savedUser.id);
            return {
                success: true,
                message: 'User registered successfully',
                user: savedUser.toJSON(),
                token
            };
        }
        catch (error) {
            console.error('Signup error:', error);
            return {
                success: false,
                message: 'Internal server error during signup'
            };
        }
    }
    // User login
    async login(loginData) {
        try {
            const { email, password } = loginData;
            // Find user by email
            const user = await this.userRepository.findOne({
                where: { email }
            });
            if (!user) {
                return {
                    success: false,
                    message: 'Invalid email or password'
                };
            }
            // Check password
            const isPasswordValid = await user.comparePassword(password);
            if (!isPasswordValid) {
                return {
                    success: false,
                    message: 'Invalid email or password'
                };
            }
            // Generate token
            const token = this.generateToken(user.id);
            return {
                success: true,
                message: 'Login successful',
                user: user.toJSON(),
                token
            };
        }
        catch (error) {
            console.error('Login error:', error);
            return {
                success: false,
                message: 'Internal server error during login'
            };
        }
    }
    // Get user by ID
    async getUserById(userId) {
        try {
            const user = await this.userRepository.findOne({
                where: { id: userId }
            });
            return user;
        }
        catch (error) {
            console.error('Get user error:', error);
            return null;
        }
    }
    // Update user profile
    async updateProfile(userId, updateData) {
        try {
            const user = await this.userRepository.findOne({
                where: { id: userId }
            });
            if (!user) {
                return {
                    success: false,
                    message: 'User not found'
                };
            }
            // Update allowed fields
            const allowedFields = ['username', 'phone', 'role'];
            allowedFields.forEach(field => {
                if (updateData[field] !== undefined) {
                    user[field] = updateData[field];
                }
            });
            const updatedUser = await this.userRepository.save(user);
            return {
                success: true,
                message: 'Profile updated successfully',
                user: updatedUser.toJSON()
            };
        }
        catch (error) {
            console.error('Update profile error:', error);
            return {
                success: false,
                message: 'Internal server error during profile update'
            };
        }
    }
    // Change password
    async changePassword(userId, currentPassword, newPassword) {
        try {
            const user = await this.userRepository.findOne({
                where: { id: userId }
            });
            if (!user) {
                return {
                    success: false,
                    message: 'User not found'
                };
            }
            // Verify current password
            const isCurrentPasswordValid = await user.comparePassword(currentPassword);
            if (!isCurrentPasswordValid) {
                return {
                    success: false,
                    message: 'Current password is incorrect'
                };
            }
            // Update password (will be hashed by @BeforeUpdate)
            user.password = newPassword;
            await this.userRepository.save(user);
            return {
                success: true,
                message: 'Password changed successfully'
            };
        }
        catch (error) {
            console.error('Change password error:', error);
            return {
                success: false,
                message: 'Internal server error during password change'
            };
        }
    }
}
exports.AuthService = AuthService;
