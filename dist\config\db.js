"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.closeDatabase = exports.initializeDatabase = exports.AppDataSource = void 0;
require("reflect-metadata");
const typeorm_1 = require("typeorm");
const user_model_1 = require("../models/user.model");
// Database configuration based on environment variables
const getDatabaseConfig = () => {
    const dbType = process.env.DB_TYPE || 'mysql'; // Default to MySQL
    const baseConfig = {
        entities: [user_model_1.User],
        synchronize: process.env.NODE_ENV !== 'production', // Auto-sync in development
        logging: process.env.NODE_ENV === 'development',
        migrations: process.env.NODE_ENV === 'production'
            ? ['dist/migrations/*.js']
            : ['src/migrations/*.ts'],
        subscribers: process.env.NODE_ENV === 'production'
            ? ['dist/subscribers/*.js']
            : ['src/subscribers/*.ts'],
    };
    switch (dbType.toLowerCase()) {
        case 'mysql':
        case 'mariadb':
            return {
                type: dbType,
                host: process.env.DB_HOST || 'localhost',
                port: parseInt(process.env.DB_PORT || '3306'),
                username: process.env.DB_USERNAME || 'root',
                password: process.env.DB_PASSWORD || '',
                database: process.env.DB_NAME || 'jameela_db',
                charset: 'utf8mb4',
                timezone: '+00:00',
                ...baseConfig,
            };
        case 'postgres':
        case 'postgresql':
            return {
                type: 'postgres',
                host: process.env.DB_HOST || 'localhost',
                port: parseInt(process.env.DB_PORT || '5432'),
                username: process.env.DB_USERNAME || 'postgres',
                password: process.env.DB_PASSWORD || '',
                database: process.env.DB_NAME || 'jameela_db',
                schema: process.env.DB_SCHEMA || 'public',
                ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
                ...baseConfig,
            };
        case 'sqlite':
            return {
                type: 'sqlite',
                database: process.env.DB_PATH || './database.sqlite',
                ...baseConfig,
            };
        default:
            throw new Error(`Unsupported database type: ${dbType}`);
    }
};
// Create DataSource instance
exports.AppDataSource = new typeorm_1.DataSource(getDatabaseConfig());
// Initialize database connection
const initializeDatabase = async () => {
    try {
        if (!exports.AppDataSource.isInitialized) {
            await exports.AppDataSource.initialize();
            console.log('✅ Database connection established successfully');
            if (process.env.NODE_ENV === 'development') {
                console.log(`📊 Database: ${exports.AppDataSource.options.type}`);
                if ('host' in exports.AppDataSource.options) {
                    console.log(`🌐 Host: ${exports.AppDataSource.options.host}:${exports.AppDataSource.options.port}`);
                }
                if ('database' in exports.AppDataSource.options) {
                    console.log(`💾 Database: ${exports.AppDataSource.options.database}`);
                }
            }
        }
    }
    catch (error) {
        console.error('❌ Error during database initialization:', error);
        throw error;
    }
};
exports.initializeDatabase = initializeDatabase;
// Close database connection
const closeDatabase = async () => {
    try {
        if (exports.AppDataSource.isInitialized) {
            await exports.AppDataSource.destroy();
            console.log('✅ Database connection closed successfully');
        }
    }
    catch (error) {
        console.error('❌ Error during database closure:', error);
        throw error;
    }
};
exports.closeDatabase = closeDatabase;
// Export default
exports.default = exports.AppDataSource;
