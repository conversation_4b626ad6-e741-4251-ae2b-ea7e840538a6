import 'user.dart';

class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final List<String>? errors;

  ApiResponse({
    required this.success,
    required this.message,
    this.data,
    this.errors,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json, T Function(Map<String, dynamic>)? fromJsonT) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null && fromJsonT != null 
          ? fromJsonT(json['data']) 
          : null,
      errors: json['errors'] != null 
          ? List<String>.from(json['errors']) 
          : null,
    );
  }
}

class AuthResponse {
  final bool success;
  final String message;
  final User? user;
  final String? token;
  final List<String>? errors;

  AuthResponse({
    required this.success,
    required this.message,
    this.user,
    this.token,
    this.errors,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      user: json['user'] != null ? User.fromJson(json['user']) : null,
      token: json['token'],
      errors: json['errors'] != null 
          ? List<String>.from(json['errors']) 
          : null,
    );
  }

  @override
  String toString() {
    return 'AuthResponse(success: $success, message: $message, user: $user, token: ${token != null ? '[TOKEN]' : 'null'})';
  }
}

class LoginRequest {
  final String email;
  final String password;

  LoginRequest({
    required this.email,
    required this.password,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
    };
  }
}

class SignupRequest {
  final String username;
  final String email;
  final String password;
  final String? phone;
  final String? role;

  SignupRequest({
    required this.username,
    required this.email,
    required this.password,
    this.phone,
    this.role,
  });

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'email': email,
      'password': password,
      if (phone != null) 'phone': phone,
      if (role != null) 'role': role,
    };
  }
}
