"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationUtils = void 0;
class ValidationUtils {
    // Email validation
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    // Password validation
    static isValidPassword(password) {
        const errors = [];
        if (password.length < 8) {
            errors.push('Password must be at least 8 characters long');
        }
        if (!/(?=.*[a-z])/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }
        if (!/(?=.*[A-Z])/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }
        if (!/(?=.*\d)/.test(password)) {
            errors.push('Password must contain at least one number');
        }
        if (!/(?=.*[@$!%*?&])/.test(password)) {
            errors.push('Password must contain at least one special character (@$!%*?&)');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    // Username validation
    static isValidUsername(username) {
        const errors = [];
        if (username.length < 3) {
            errors.push('Username must be at least 3 characters long');
        }
        if (username.length > 20) {
            errors.push('Username must be no more than 20 characters long');
        }
        if (!/^[a-zA-Z0-9_]+$/.test(username)) {
            errors.push('Username can only contain letters, numbers, and underscores');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    // Phone validation (basic)
    static isValidPhone(phone) {
        if (!phone)
            return true; // Phone is optional
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
    }
    // Validate signup data
    static validateSignupData(data) {
        const errors = [];
        // Required fields
        if (!data.username) {
            errors.push('Username is required');
        }
        else {
            const usernameValidation = this.isValidUsername(data.username);
            if (!usernameValidation.isValid) {
                errors.push(...usernameValidation.errors);
            }
        }
        if (!data.email) {
            errors.push('Email is required');
        }
        else if (!this.isValidEmail(data.email)) {
            errors.push('Please provide a valid email address');
        }
        if (!data.password) {
            errors.push('Password is required');
        }
        else {
            const passwordValidation = this.isValidPassword(data.password);
            if (!passwordValidation.isValid) {
                errors.push(...passwordValidation.errors);
            }
        }
        // Optional fields
        if (data.phone && !this.isValidPhone(data.phone)) {
            errors.push('Please provide a valid phone number');
        }
        if (data.role && !['Student', 'Teacher', 'Admin'].includes(data.role)) {
            errors.push('Role must be one of: Student, Teacher, Admin');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    // Validate login data
    static validateLoginData(data) {
        const errors = [];
        if (!data.email) {
            errors.push('Email is required');
        }
        else if (!this.isValidEmail(data.email)) {
            errors.push('Please provide a valid email address');
        }
        if (!data.password) {
            errors.push('Password is required');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    // Validate change password data
    static validateChangePasswordData(data) {
        const errors = [];
        if (!data.currentPassword) {
            errors.push('Current password is required');
        }
        if (!data.newPassword) {
            errors.push('New password is required');
        }
        else {
            const passwordValidation = this.isValidPassword(data.newPassword);
            if (!passwordValidation.isValid) {
                errors.push(...passwordValidation.errors);
            }
        }
        if (data.currentPassword === data.newPassword) {
            errors.push('New password must be different from current password');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    // Sanitize input (basic)
    static sanitizeString(input) {
        return input.trim().replace(/[<>]/g, '');
    }
    // Validate update profile data
    static validateUpdateProfileData(data) {
        const errors = [];
        if (data.username) {
            const usernameValidation = this.isValidUsername(data.username);
            if (!usernameValidation.isValid) {
                errors.push(...usernameValidation.errors);
            }
        }
        if (data.phone && !this.isValidPhone(data.phone)) {
            errors.push('Please provide a valid phone number');
        }
        if (data.role && !['Student', 'Teacher', 'Admin'].includes(data.role)) {
            errors.push('Role must be one of: Student, Teacher, Admin');
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
exports.ValidationUtils = ValidationUtils;
