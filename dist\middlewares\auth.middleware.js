"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authMiddleware = exports.AuthMiddleware = void 0;
const auth_service_1 = require("../services/auth.service");
class AuthMiddleware {
    constructor() {
        this.authService = new auth_service_1.AuthService();
        // Middleware to verify JWT token
        this.verifyToken = async (req, res, next) => {
            try {
                const authHeader = req.headers.authorization;
                if (!authHeader) {
                    res.status(401).json({
                        success: false,
                        message: 'Access token is required'
                    });
                    return;
                }
                const token = authHeader.split(' ')[1]; // Bearer TOKEN
                if (!token) {
                    res.status(401).json({
                        success: false,
                        message: 'Access token is required'
                    });
                    return;
                }
                // Verify token
                const decoded = this.authService.verifyToken(token);
                // Get user from database
                const user = await this.authService.getUserById(decoded.userId);
                if (!user) {
                    res.status(401).json({
                        success: false,
                        message: 'Invalid token - user not found'
                    });
                    return;
                }
                // Add user to request object
                req.user = user;
                next();
            }
            catch (error) {
                console.error('Token verification error:', error);
                res.status(401).json({
                    success: false,
                    message: 'Invalid or expired token'
                });
            }
        };
        // Middleware to check user roles
        this.requireRole = (roles) => {
            return (req, res, next) => {
                if (!req.user) {
                    return res.status(401).json({
                        success: false,
                        message: 'Authentication required'
                    });
                }
                if (!roles.includes(req.user.role)) {
                    return res.status(403).json({
                        success: false,
                        message: 'Insufficient permissions'
                    });
                }
                next();
            };
        };
        // Optional authentication (doesn't fail if no token)
        this.optionalAuth = async (req, res, next) => {
            try {
                const authHeader = req.headers.authorization;
                if (authHeader) {
                    const token = authHeader.split(' ')[1];
                    if (token) {
                        const decoded = this.authService.verifyToken(token);
                        const user = await this.authService.getUserById(decoded.userId);
                        if (user) {
                            req.user = user;
                        }
                    }
                }
                next();
            }
            catch (error) {
                // Continue without authentication
                next();
            }
        };
    }
}
exports.AuthMiddleware = AuthMiddleware;
// Export instance
exports.authMiddleware = new AuthMiddleware();
