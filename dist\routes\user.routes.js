"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const user_controller_1 = require("../controllers/user.controller");
const auth_middleware_1 = require("../middlewares/auth.middleware");
const router = (0, express_1.Router)();
// Public routes (no authentication required)
router.post('/signup', user_controller_1.signup);
router.post('/register', user_controller_1.registerUser); // Alias for backward compatibility
router.post('/login', user_controller_1.login);
// Protected routes (authentication required)
router.get('/profile', auth_middleware_1.authMiddleware.verifyToken, user_controller_1.getProfile);
router.put('/profile', auth_middleware_1.authMiddleware.verifyToken, user_controller_1.updateProfile);
router.post('/change-password', auth_middleware_1.authMiddleware.verifyToken, user_controller_1.changePassword);
router.post('/logout', auth_middleware_1.authMiddleware.verifyToken, user_controller_1.logout);
exports.default = router;
