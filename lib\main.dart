import 'package:flutter/material.dart';
import 'theme/app_theme.dart';
import 'screens/splash_screen.dart';

void main() {
  runApp(const JameelaApp());
}

class JameelaApp extends StatelessWidget {
  const JameelaApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Jameela - Beauty Appointments',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system, // Follows system theme
      home: const SplashScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
