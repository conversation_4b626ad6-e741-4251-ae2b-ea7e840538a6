import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/api_response.dart';
import '../models/user.dart';
import 'storage_service.dart';

class AuthService {
  static const String baseUrl = 'http://localhost:8000/api';
  
  // Headers for API requests
  static Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Headers with authentication token
  static Future<Map<String, String>> get _authHeaders async {
    final token = await StorageService.getToken();
    return {
      ..._headers,
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // User signup
  static Future<AuthResponse> signup(SignupRequest request) async {
    try {
      print('Attempting signup for: ${request.email}');
      
      final response = await http.post(
        Uri.parse('$baseUrl/signup'),
        headers: _headers,
        body: jsonEncode(request.toJson()),
      );

      print('Signup response status: ${response.statusCode}');
      print('Signup response body: ${response.body}');

      final Map<String, dynamic> responseData = jsonDecode(response.body);
      final authResponse = AuthResponse.fromJson(responseData);

      // If signup successful, save token and user data
      if (authResponse.success && authResponse.token != null && authResponse.user != null) {
        await StorageService.saveToken(authResponse.token!);
        await StorageService.saveUserData(
          userId: authResponse.user!.id,
          email: authResponse.user!.email,
          username: authResponse.user!.username,
        );
      }

      return authResponse;
    } catch (e) {
      print('Signup error: $e');
      return AuthResponse(
        success: false,
        message: 'Network error. Please check your connection and try again.',
      );
    }
  }

  // User login
  static Future<AuthResponse> login(LoginRequest request) async {
    try {
      print('Attempting login for: ${request.email}');
      
      final response = await http.post(
        Uri.parse('$baseUrl/login'),
        headers: _headers,
        body: jsonEncode(request.toJson()),
      );

      print('Login response status: ${response.statusCode}');
      print('Login response body: ${response.body}');

      final Map<String, dynamic> responseData = jsonDecode(response.body);
      final authResponse = AuthResponse.fromJson(responseData);

      // If login successful, save token and user data
      if (authResponse.success && authResponse.token != null && authResponse.user != null) {
        await StorageService.saveToken(authResponse.token!);
        await StorageService.saveUserData(
          userId: authResponse.user!.id,
          email: authResponse.user!.email,
          username: authResponse.user!.username,
        );
      }

      return authResponse;
    } catch (e) {
      print('Login error: $e');
      return AuthResponse(
        success: false,
        message: 'Network error. Please check your connection and try again.',
      );
    }
  }

  // Logout user
  static Future<bool> logout() async {
    try {
      // Optional: Call backend logout endpoint
      final token = await StorageService.getToken();
      if (token != null) {
        try {
          await http.post(
            Uri.parse('$baseUrl/logout'),
            headers: await _authHeaders,
          );
        } catch (e) {
          print('Backend logout error (continuing with local logout): $e');
        }
      }

      // Clear local storage
      return await StorageService.clearAll();
    } catch (e) {
      print('Logout error: $e');
      return false;
    }
  }

  // Check if user is authenticated
  static Future<bool> isAuthenticated() async {
    return await StorageService.isLoggedIn();
  }

  // Get current user data from storage
  static Future<User?> getCurrentUser() async {
    try {
      final userId = await StorageService.getUserId();
      final email = await StorageService.getUserEmail();
      final username = await StorageService.getUsername();

      if (userId != null && email != null && username != null) {
        return User(
          id: userId,
          username: username,
          email: email,
          phone: '', // We don't store phone locally for now
          role: 'Student', // Default role
        );
      }
      return null;
    } catch (e) {
      print('Error getting current user: $e');
      return null;
    }
  }

  // Validate email format
  static bool isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }

  // Validate password strength
  static bool isValidPassword(String password) {
    // At least 6 characters
    return password.length >= 6;
  }

  // Validate username
  static bool isValidUsername(String username) {
    // At least 3 characters, alphanumeric and underscore only
    return RegExp(r'^[a-zA-Z0-9_]{3,}$').hasMatch(username);
  }
}
