{"name": "@sqltools/formatter", "version": "1.2.5", "description": "Formats SQL queries. Part of SQLTools", "license": "MIT", "main": "./lib/sqlFormatter.js", "types": "./lib/sqlFormatter.d.ts", "private": false, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "keywords": ["sql", "formatter", "format", "n1ql", "whitespaces", "sqltools"], "authors": ["<PERSON><PERSON> <<EMAIL>>"], "files": ["lib/**/*"], "scripts": {"test:tsc": "tsc --noEmit -p ./tsconfig.json --skipL<PERSON><PERSON><PERSON><PERSON>", "clean": "rimraf lib dist *.tgz", "test": "jest --config jest.config.js", "test:watch": "yarn run test -- --watch", "check": "yarn run test", "precompile": "yarn run check && yarn run clean", "compile": "tsc -p ./tsconfig.json", "build": "yarn run compile", "prepare": "yarn run build", "prepack": "yarn run build", "release": "npm pack && npm publish *.tgz --tag latest --access public", "beta": "npm pack && npm publish *.tgz --tag beta --access public", "tag": "git tag  formatter/$(node -e 'console.log(require(\"./package.json\").version)')"}, "repository": {"type": "git", "url": "https://github.com/mtxr/vscode-sqltools.git", "directory": "packages/formatter"}, "bugs": {"url": "https://github.com/mtxr/vscode-sqltools/labels/formatting"}, "devDependencies": {"@types/jest": "^24.0.11", "dedent-js": "^1.0.1", "jest": "^26.6.3", "jest-cli": "^26.6.3", "ts-jest": "^26.5.4", "rimraf": "^3.0.0", "typescript": "~4.8.3"}}