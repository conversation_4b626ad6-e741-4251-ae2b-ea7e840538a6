"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerUser = exports.logout = exports.changePassword = exports.updateProfile = exports.getProfile = exports.login = exports.signup = void 0;
const auth_service_1 = require("../services/auth.service");
const validation_1 = require("../utils/validation");
const authService = new auth_service_1.AuthService();
// User registration/signup
const signup = async (req, res) => {
    try {
        // Validate input data
        const validation = validation_1.ValidationUtils.validateSignupData(req.body);
        if (!validation.isValid) {
            res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: validation.errors
            });
            return;
        }
        // Sanitize input
        const signupData = {
            username: validation_1.ValidationUtils.sanitizeString(req.body.username),
            email: validation_1.ValidationUtils.sanitizeString(req.body.email.toLowerCase()),
            password: req.body.password,
            phone: req.body.phone ? validation_1.ValidationUtils.sanitizeString(req.body.phone) : undefined,
            role: req.body.role || 'Student'
        };
        // Call auth service
        const result = await authService.signup(signupData);
        if (result.success) {
            res.status(201).json(result);
        }
        else {
            res.status(400).json(result);
        }
    }
    catch (error) {
        console.error('Signup controller error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.signup = signup;
// User login
const login = async (req, res) => {
    try {
        // Validate input data
        const validation = validation_1.ValidationUtils.validateLoginData(req.body);
        if (!validation.isValid) {
            res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: validation.errors
            });
            return;
        }
        // Sanitize input
        const loginData = {
            email: validation_1.ValidationUtils.sanitizeString(req.body.email.toLowerCase()),
            password: req.body.password
        };
        // Call auth service
        const result = await authService.login(loginData);
        if (result.success) {
            res.status(200).json(result);
        }
        else {
            res.status(401).json(result);
        }
    }
    catch (error) {
        console.error('Login controller error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.login = login;
// Get current user profile
const getProfile = async (req, res) => {
    try {
        const user = req.user;
        if (!user) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }
        res.status(200).json({
            success: true,
            message: 'Profile retrieved successfully',
            user: user.toJSON ? user.toJSON() : user
        });
    }
    catch (error) {
        console.error('Get profile controller error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.getProfile = getProfile;
// Update user profile
const updateProfile = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }
        // Validate input data
        const validation = validation_1.ValidationUtils.validateUpdateProfileData(req.body);
        if (!validation.isValid) {
            res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: validation.errors
            });
            return;
        }
        // Sanitize input
        const updateData = {};
        if (req.body.username) {
            updateData.username = validation_1.ValidationUtils.sanitizeString(req.body.username);
        }
        if (req.body.phone) {
            updateData.phone = validation_1.ValidationUtils.sanitizeString(req.body.phone);
        }
        if (req.body.role) {
            updateData.role = req.body.role;
        }
        // Call auth service
        const result = await authService.updateProfile(userId, updateData);
        if (result.success) {
            res.status(200).json(result);
        }
        else {
            res.status(400).json(result);
        }
    }
    catch (error) {
        console.error('Update profile controller error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.updateProfile = updateProfile;
// Change password
const changePassword = async (req, res) => {
    try {
        const userId = req.user?.id;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'User not authenticated'
            });
            return;
        }
        // Validate input data
        const validation = validation_1.ValidationUtils.validateChangePasswordData(req.body);
        if (!validation.isValid) {
            res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: validation.errors
            });
            return;
        }
        // Call auth service
        const result = await authService.changePassword(userId, req.body.currentPassword, req.body.newPassword);
        if (result.success) {
            res.status(200).json(result);
        }
        else {
            res.status(400).json(result);
        }
    }
    catch (error) {
        console.error('Change password controller error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.changePassword = changePassword;
// Logout
const logout = async (req, res) => {
    try {
        // In a JWT-based system, logout is typically handled client-side
        // by removing the token. Here we just acknowledge the logout.
        res.status(200).json({
            success: true,
            message: 'Logged out successfully'
        });
    }
    catch (error) {
        console.error('Logout controller error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};
exports.logout = logout;
// For backward compatibility
exports.registerUser = exports.signup;
